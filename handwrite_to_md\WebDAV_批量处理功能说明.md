# WebDAV 批量处理功能说明

## 功能概述

本次更新为手写识别转Markdown应用添加了完整的WebDAV批量处理功能，允许用户直接处理存储在WebDAV服务器上的图片文件。

## 主要特性

### 1. WebDAV 连接管理
- **服务器配置**：支持配置WebDAV服务器地址、用户名、密码和远程路径
- **连接测试**：提供连接测试功能，确保配置正确
- **安全存储**：敏感信息（密码）使用加密存储

### 2. 文件预览和选择
- **远程文件列表**：自动获取WebDAV服务器上的图片文件
- **文件预览**：显示文件名、大小、修改时间等信息
- **批量选择**：支持全选、取消全选和单独选择文件
- **格式支持**：支持 jpg, jpeg, png, bmp, gif, webp 格式

### 3. 智能下载机制
- **临时存储**：文件下载到本地临时目录，不占用用户存储空间
- **进度显示**：实时显示下载进度和当前处理文件
- **错误处理**：单个文件下载失败不影响其他文件处理
- **自动清理**：处理完成后自动清理临时文件

### 4. 批量处理集成
- **无缝集成**：复用现有批量处理界面和逻辑
- **分组管理**：支持图片分组、拖拽排序等功能
- **预览功能**：支持图片放大预览和详情查看
- **忽略区域**：可将不需要处理的图片拖拽到忽略区域

### 5. 远程文件管理
- **同步删除**：删除操作同时删除本地临时文件和远程文件
- **批量删除**：支持批量删除选中的文件
- **远端删除**：在文件预览页面直接删除WebDAV服务器上的文件
- **安全确认**：删除操作需要用户确认，防止误操作
- **删除进度**：实时显示删除进度和当前处理文件

### 6. 临时文件管理
- **自动清理**：定期清理过期的临时文件（默认24小时）
- **大小限制**：限制缓存大小（默认100MB），超出时自动清理最旧文件
- **会话管理**：每次批量处理使用独立的会话目录
- **启动维护**：应用启动时自动执行临时文件维护

## 使用流程

### 1. 配置WebDAV
1. 打开应用设置页面
2. 在"WebDAV配置"区域填写：
   - WebDAV服务器地址
   - 用户名和密码
   - 远程路径（可选，默认为根目录）
3. 点击"测试连接"验证配置
4. 保存设置

### 2. 批量处理WebDAV文件
1. 在主页面点击"批量处理WebDAV"按钮
2. 等待连接WebDAV服务器并加载文件列表
3. 选择需要处理的图片文件
4. **可选操作**：点击"远端删除"按钮直接删除选中的远程文件
5. 点击"下载并处理"按钮
6. 等待文件下载完成
7. 在批量处理预览界面中：
   - 调整图片分组
   - 设置图片顺序
   - 将不需要的图片拖拽到忽略区域
8. 点击"开始处理"进行LLM识别
9. 处理完成后选择是否删除远程文件

### 3. 远端删除功能
1. 在WebDAV文件预览页面选择要删除的文件
2. 点击右上角的"远端删除"按钮（垃圾桶图标）
3. 确认删除操作（此操作不可恢复）
4. 等待删除完成，查看删除结果

## 技术实现

### 核心组件
- **WebDAVService**：WebDAV连接和文件操作服务
- **TempFileManager**：临时文件管理服务
- **WebDAVBatchProcessPage**：WebDAV文件预览和下载页面
- **BatchProcessPage**：扩展支持WebDAV模式的批量处理页面

### 数据流程
1. 用户配置 → 加密存储 → 连接测试
2. 连接WebDAV → 获取文件列表 → 用户选择
3. 批量下载 → 临时存储 → 批量处理预览
4. LLM处理 → 保存结果 → 清理临时文件
5. 可选：删除远程文件

### 安全特性
- 密码加密存储
- 临时文件自动清理
- 网络错误处理
- 用户操作确认

## 配置要求

### WebDAV服务器
- 支持标准WebDAV协议
- 提供用户认证
- 允许文件读取和删除操作

### 网络环境
- 稳定的网络连接
- 足够的带宽支持文件下载

### 设备要求
- 足够的临时存储空间
- 支持文件管理权限

## 注意事项

1. **网络依赖**：功能需要稳定的网络连接
2. **存储空间**：确保设备有足够的临时存储空间
3. **权限要求**：需要文件访问权限
4. **数据安全**：删除远程文件操作不可恢复
5. **性能考虑**：大量文件下载可能需要较长时间

## 故障排除

### 连接失败
- 检查WebDAV服务器地址是否正确
- 验证用户名和密码
- 确认网络连接正常
- 检查服务器是否支持WebDAV协议

### 下载失败
- 检查网络连接稳定性
- 确认设备存储空间充足
- 验证文件访问权限
- 重试下载操作

### 处理异常
- 检查LLM配置是否正确
- 确认图片格式支持
- 查看错误提示信息
- 重启应用重试

## 更新内容

### 新增文件
- `lib/services/webdav_service.dart` - WebDAV服务层
- `lib/services/temp_file_manager.dart` - 临时文件管理器
- `lib/pages/webdav_batch_process_page.dart` - WebDAV批量处理页面

### 修改文件
- `lib/models/app_settings.dart` - 添加WebDAV配置字段
- `lib/services/settings_repository.dart` - 添加WebDAV设置存储
- `lib/pages/settings_page.dart` - 添加WebDAV配置界面
- `lib/pages/batch_process_page.dart` - 扩展支持WebDAV模式
- `lib/pages/working_home_page.dart` - 添加WebDAV批量处理按钮
- `pubspec.yaml` - 添加webdav_client依赖

### 功能增强
- 主页面状态卡片显示WebDAV配置状态
- 批量处理支持远程文件删除
- **新增远端删除功能**：在文件预览页面直接删除远程文件
- 临时文件自动维护和清理
- 用户体验优化和错误处理改进

### 最新更新（远端删除功能）
- 在WebDAV批量处理页面添加"远端删除"按钮
- 支持批量选择和删除远程文件
- 删除确认对话框，防止误操作
- 实时删除进度显示
- 删除成功后自动更新文件列表
