1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.handwrite_to_md"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- 相机权限 -->
11    <uses-permission android:name="android.permission.CAMERA" />
11-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:3:5-65
11-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:3:22-62
12    <!-- 存储权限 -->
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:5:5-80
13-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:5:22-77
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:6:5-81
14-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:6:22-78
15    <!-- Android 13+ 照片权限 -->
16    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
16-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:5-76
16-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:8:22-73
17    <!-- Android 11+ 管理外部存储权限 -->
18    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
18-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:10:5-82
18-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:10:22-79
19    <!-- 网络权限 -->
20    <uses-permission android:name="android.permission.INTERNET" />
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:12:5-67
20-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:12:22-64
21    <!-- 网络状态权限 -->
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:5-79
22-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:14:22-76
23    <!--
24         Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:53:5-58:15
31        <intent>
31-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:54:9-57:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:55:13-72
32-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:55:21-70
33
34            <data android:mimeType="text/plain" />
34-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:13-50
34-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:19-48
35        </intent>
36        <intent>
36-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
37            <action android:name="android.intent.action.GET_CONTENT" />
37-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
37-->[:file_picker] D:\android\handwritetomd\handwrite_to_md\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
38
39            <data android:mimeType="*/*" />
39-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:13-50
39-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:56:19-48
40        </intent>
41    </queries>
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.example.handwrite_to_md.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
50-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:18:9-42
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:extractNativeLibs="true"
53        android:icon="@mipmap/ic_launcher"
53-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:19:9-43
54        android:label="ToMDs" >
54-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:17:9-30
55        <activity
55-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:20:9-41:20
56            android:name="com.example.handwrite_to_md.MainActivity"
56-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:21:13-41
57            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:26:13-163
58            android:exported="true"
58-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:22:13-36
59            android:hardwareAccelerated="true"
59-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:27:13-47
60            android:launchMode="singleTop"
60-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:23:13-43
61            android:taskAffinity=""
61-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:24:13-36
62            android:theme="@style/LaunchTheme"
62-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:25:13-47
63            android:windowSoftInputMode="adjustResize" >
63-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:28:13-55
64
65            <!--
66                 Specifies an Android theme to apply to this Activity as soon as
67                 the Android process has started. This theme is visible to the user
68                 while the Flutter UI initializes. After that, this theme continues
69                 to determine the Window background behind the Flutter UI.
70            -->
71            <meta-data
71-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:33:13-36:17
72                android:name="io.flutter.embedding.android.NormalTheme"
72-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:34:15-70
73                android:resource="@style/NormalTheme" />
73-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:35:15-52
74
75            <intent-filter>
75-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:37:13-40:29
76                <action android:name="android.intent.action.MAIN" />
76-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:38:17-68
76-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:38:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:39:17-76
78-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:39:27-74
79            </intent-filter>
80        </activity>
81        <!--
82             Don't delete the meta-data below.
83             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
84        -->
85        <meta-data
85-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:44:9-46:33
86            android:name="flutterEmbedding"
86-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:45:13-44
87            android:value="2" />
87-->D:\android\handwritetomd\handwrite_to_md\android\app\src\main\AndroidManifest.xml:46:13-30
88
89        <provider
89-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
90            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
90-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
91            android:authorities="com.example.handwrite_to_md.flutter.image_provider"
91-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
92            android:exported="false"
92-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
93            android:grantUriPermissions="true" >
93-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
94            <meta-data
94-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
95                android:name="android.support.FILE_PROVIDER_PATHS"
95-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
96                android:resource="@xml/flutter_image_picker_file_paths" />
96-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
97        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
98        <service
98-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
99            android:name="com.google.android.gms.metadata.ModuleDependencies"
99-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
100            android:enabled="false"
100-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
101            android:exported="false" >
101-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
102            <intent-filter>
102-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
103                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
103-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
103-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
104            </intent-filter>
105
106            <meta-data
106-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
107                android:name="photopicker_activity:0:required"
107-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
108                android:value="" />
108-->[:image_picker_android] D:\android\handwritetomd\handwrite_to_md\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
109        </service>
110
111        <uses-library
111-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
112            android:name="androidx.window.extensions"
112-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
113            android:required="false" />
113-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
114        <uses-library
114-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
115            android:name="androidx.window.sidecar"
115-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
116            android:required="false" />
116-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
117
118        <provider
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
119            android:name="androidx.startup.InitializationProvider"
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
120            android:authorities="com.example.handwrite_to_md.androidx-startup"
120-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
121            android:exported="false" >
121-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
122            <meta-data
122-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
123                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
123-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
124                android:value="androidx.startup" />
124-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
125            <meta-data
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
127                android:value="androidx.startup" />
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
128        </provider>
129
130        <receiver
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
131            android:name="androidx.profileinstaller.ProfileInstallReceiver"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
132            android:directBootAware="false"
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
133            android:enabled="true"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
134            android:exported="true"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
135            android:permission="android.permission.DUMP" >
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
137                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
138            </intent-filter>
139            <intent-filter>
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
140                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
141            </intent-filter>
142            <intent-filter>
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
143                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
144            </intent-filter>
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
146                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
147            </intent-filter>
148        </receiver>
149    </application>
150
151</manifest>
