io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.window.extensions.core.util.function.Function
androidx.lifecycle.ReportFragment$LifecycleCallbacks
androidx.versionedparcelable.CustomVersionedParcelable
androidx.preference.PreferenceCategory
io.flutter.view.AccessibilityViewEmbedder
androidx.profileinstaller.ProfileInstallerInitializer
androidx.versionedparcelable.ParcelImpl
androidx.appcompat.widget.ViewStubCompat
androidx.appcompat.app.AlertController$RecycleListView
androidx.recyclerview.widget.RecyclerView
androidx.lifecycle.ReportFragment
io.flutter.plugin.text.ProcessTextPlugin
androidx.window.extensions.core.util.function.Consumer
io.flutter.view.TextureRegistry$GLTextureConsumer
androidx.preference.SwitchPreferenceCompat
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
io.flutter.view.TextureRegistry$ImageTextureEntry
androidx.appcompat.widget.AlertDialogLayout
androidx.core.app.RemoteActionCompatParcelizer
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
androidx.recyclerview.widget.LinearLayoutManager
androidx.appcompat.widget.ButtonBarLayout
androidx.appcompat.widget.ContentFrameLayout
androidx.preference.PreferenceGroup
androidx.core.widget.NestedScrollView
io.flutter.embedding.engine.FlutterJNI
androidx.appcompat.view.menu.ListMenuItemView
androidx.preference.DropDownPreference
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
androidx.appcompat.view.menu.ExpandedMenuView
androidx.appcompat.widget.ActionBarContainer
androidx.appcompat.widget.Toolbar
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
android.support.v4.app.RemoteActionCompatParcelizer
androidx.lifecycle.DefaultLifecycleObserver
androidx.appcompat.widget.ActivityChooserView$InnerLayout
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
com.mr.flutter.plugin.filepicker.FilePickerPlugin
androidx.preference.DialogPreference
androidx.recyclerview.widget.StaggeredGridLayoutManager
androidx.core.graphics.drawable.IconCompat
androidx.preference.SwitchPreference
androidx.preference.SeekBarPreference
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
kotlinx.coroutines.android.AndroidDispatcherFactory
io.flutter.view.FlutterCallbackInformation
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.annotation.Keep
androidx.core.graphics.drawable.IconCompatParcelizer
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.preference.TwoStatePreference
androidx.appcompat.widget.DialogTitle
androidx.appcompat.widget.ActionMenuView
androidx.preference.ListPreference
io.flutter.view.TextureRegistry$SurfaceTextureEntry
androidx.recyclerview.widget.GridLayoutManager
io.flutter.plugin.platform.SingleViewPresentation
androidx.startup.InitializationProvider
com.tekartik.sqflite.SqflitePlugin
com.example.handwrite_to_md.MainActivity
kotlinx.coroutines.internal.StackTraceRecoveryKt
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
androidx.preference.internal.PreferenceImageView
kotlin.coroutines.jvm.internal.BaseContinuationImpl
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.preference.PreferenceScreen
androidx.window.extensions.core.util.function.Predicate
androidx.appcompat.widget.SearchView
androidx.appcompat.widget.ActionBarOverlayLayout
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.core.app.CoreComponentFactory
io.flutter.plugins.GeneratedPluginRegistrant
androidx.lifecycle.ProcessLifecycleInitializer
androidx.preference.Preference
androidx.appcompat.widget.ActionBarContextView
com.baseflow.permissionhandler.PermissionHandlerPlugin
io.flutter.plugins.imagepicker.ImagePickerFileProvider
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
androidx.preference.UnPressableLinearLayout
androidx.preference.EditTextPreference
androidx.preference.CheckBoxPreference
androidx.appcompat.widget.SwitchCompat
androidx.profileinstaller.ProfileInstallReceiver
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
androidx.core.app.RemoteActionCompat
io.flutter.plugins.pathprovider.PathProviderPlugin
com.it_nomads.fluttersecurestorage.FlutterSecureStoragePlugin
io.flutter.embedding.engine.FlutterOverlaySurface
io.flutter.view.TextureRegistry$ImageConsumer
androidx.lifecycle.ProcessLifecycleOwner$attach$1
androidx.preference.MultiSelectListPreference
io.flutter.view.TextureRegistry$SurfaceProducer
io.flutter.plugins.imagepicker.ImagePickerPlugin
androidx.appcompat.widget.FitWindowsLinearLayout
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
androidx.appcompat.view.menu.ActionMenuItemView
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.proto.KmsAeadKeyFormat params_
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: int VERSION_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
com.google.crypto.tink.proto.HmacParams: int HASH_FIELD_NUMBER
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
com.google.crypto.tink.proto.HmacKey: int version_
com.google.crypto.tink.proto.AesCtrKeyFormat: int KEY_SIZE_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
com.google.crypto.tink.proto.AesSivKeyFormat: int version_
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
com.google.crypto.tink.proto.HmacParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
com.google.crypto.tink.proto.Keyset$Key: int status_
com.google.crypto.tink.proto.RegistryConfig: int CONFIG_NAME_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage lastDequeuedImage
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int version_
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.proto.AesCtrKeyFormat DEFAULT_INSTANCE
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: com.google.crypto.tink.shaded.protobuf.UnknownFieldSetLite unknownFields
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
com.google.crypto.tink.proto.EncryptedKeyset: int ENCRYPTED_KEYSET_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.proto.KeyData DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.proto.KeysetInfo keysetInfo_
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
kotlinx.coroutines.DispatchedCoroutine: int _decision
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList keyInfo_
com.google.crypto.tink.proto.AesEaxKeyFormat: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.KeyTemplate: int outputPrefixType_
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesEaxParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KmsAeadKeyFormat: com.google.crypto.tink.proto.KmsAeadKeyFormat DEFAULT_INSTANCE
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.KeyData: java.lang.String typeUrl_
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int status_
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: java.lang.String typeUrl_
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.proto.AesCmacParams params_
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
com.google.crypto.tink.proto.KeyTemplate: java.lang.String typeUrl_
com.google.crypto.tink.proto.HmacParams: int tagSize_
com.google.crypto.tink.proto.AesGcmKey: int KEY_VALUE_FIELD_NUMBER
kotlinx.coroutines.CancelledContinuation: int _resumed
com.google.crypto.tink.proto.AesGcmKeyFormat: int keySize_
com.google.crypto.tink.proto.AesCmacKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.proto.RegistryConfig DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeysetInfo: int PRIMARY_KEY_ID_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmSivKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
com.google.crypto.tink.proto.AesCmacParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesGcmSivKeyFormat: com.google.crypto.tink.proto.AesGcmSivKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: int DEK_TEMPLATE_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset: int KEY_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKeyFormat: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.HmacKeyFormat hmacKeyFormat_
com.google.crypto.tink.proto.HmacKeyFormat: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset: int PRIMARY_KEY_ID_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.proto.AesCtrKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeyTemplate: int VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.HmacKey hmacKey_
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.Keyset$Key: int KEY_DATA_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
com.google.crypto.tink.proto.AesEaxKey: int PARAMS_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
com.google.crypto.tink.proto.RegistryConfig: java.lang.String configName_
com.google.crypto.tink.proto.KeysetInfo: com.google.crypto.tink.proto.KeysetInfo DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeysetInfo: int primaryKeyId_
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int VERSION_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat: com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.shaded.protobuf.ByteString value_
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeyTypeEntry: int CATALOGUE_NAME_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmSivKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesSivKey: int VERSION_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: java.lang.String kekUri_
com.google.crypto.tink.proto.KeyData: com.google.crypto.tink.shaded.protobuf.ByteString value_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String catalogueName_
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.shaded.protobuf.ByteString encryptedKeyset_
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.proto.Keyset DEFAULT_INSTANCE
com.google.crypto.tink.proto.ChaCha20Poly1305KeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.proto.KmsEnvelopeAeadKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: int KEK_URI_FIELD_NUMBER
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.proto.AesCtrParams params_
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
com.google.crypto.tink.proto.KeyTemplate: int TYPE_URL_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.proto.AesCmacParams params_
com.google.crypto.tink.proto.HmacKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset$Key: int KEY_ID_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList key_
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int outputPrefixType_
kotlinx.coroutines.CompletedExceptionally: int _handled
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.proto.Keyset$Key DEFAULT_INSTANCE
com.google.crypto.tink.proto.KmsAeadKey: int version_
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String typeUrl_
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
com.google.crypto.tink.proto.HmacKey: int PARAMS_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
com.google.crypto.tink.proto.AesCtrKey: int VERSION_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.proto.AesCmacKeyFormat DEFAULT_INSTANCE
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int keyId_
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.proto.KeyTemplate dekTemplate_
com.google.crypto.tink.proto.HmacParams: com.google.crypto.tink.proto.HmacParams DEFAULT_INSTANCE
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
com.google.crypto.tink.proto.KeyData: int KEY_MATERIAL_TYPE_FIELD_NUMBER
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int version_
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
com.google.crypto.tink.proto.HmacKeyFormat: int version_
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeyTypeEntry: com.google.crypto.tink.proto.KeyTypeEntry DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset$Key: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.proto.ChaCha20Poly1305Key DEFAULT_INSTANCE
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.proto.EncryptedKeyset DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int HMAC_KEY_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo: int KEY_INFO_FIELD_NUMBER
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.shaded.protobuf.Internal$ProtobufList entry_
com.google.crypto.tink.proto.AesGcmKeyFormat: int VERSION_FIELD_NUMBER
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
com.google.crypto.tink.shaded.protobuf.AbstractMessageLite: int memoizedHashCode
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
com.google.crypto.tink.proto.Keyset$Key: int STATUS_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.proto.KeyTemplate DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCmacParams: int tagSize_
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
com.google.crypto.tink.proto.HmacParams: int hash_
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.proto.AesEaxParams params_
com.google.crypto.tink.proto.Keyset$Key: int outputPrefixType_
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int keySize_
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.proto.AesCtrParams params_
io.flutter.embedding.engine.FlutterOverlaySurface: int id
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
com.google.crypto.tink.proto.AesSivKey: int version_
com.google.crypto.tink.proto.KeyTypeEntry: int NEW_KEY_ALLOWED_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.KeyTemplate: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: int HMAC_KEY_FORMAT_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
com.google.crypto.tink.proto.AesGcmKeyFormat: com.google.crypto.tink.proto.AesGcmKeyFormat DEFAULT_INSTANCE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
com.google.crypto.tink.proto.AesCtrKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrParams: int IV_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCtrParams: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesEaxKey: int version_
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int version_
io.flutter.plugin.platform.SingleViewPresentation: int viewId
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.proto.HmacParams params_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.proto.AesGcmKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
com.google.crypto.tink.proto.AesGcmKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.Keyset$Key: com.google.crypto.tink.proto.KeyData keyData_
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
com.google.crypto.tink.proto.AesEaxKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: com.google.crypto.tink.proto.KeysetInfo$KeyInfo DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesGcmSivKey: com.google.crypto.tink.proto.AesGcmSivKey DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
com.google.crypto.tink.proto.KeyTypeEntry: int keyManagerVersion_
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.proto.AesCmacKey DEFAULT_INSTANCE
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.proto.KmsAeadKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCmacKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKeyFormat: java.lang.String keyUri_
com.google.crypto.tink.proto.KeyData: int VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.proto.AesSivKey DEFAULT_INSTANCE
kotlinx.coroutines.channels.BufferedChannel: long receivers
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
com.google.crypto.tink.proto.KeyTypeEntry: boolean newKeyAllowed_
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int KEY_ID_FIELD_NUMBER
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
com.google.crypto.tink.proto.AesGcmKey: int version_
com.google.crypto.tink.proto.KeyTypeEntry: int PRIMITIVE_NAME_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.XChaCha20Poly1305Key: com.google.crypto.tink.proto.XChaCha20Poly1305Key DEFAULT_INSTANCE
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int PARAMS_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.KeyTypeEntry: java.lang.String primitiveName_
com.google.crypto.tink.proto.AesCtrKeyFormat: int keySize_
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.proto.AesEaxParams params_
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKey: int version_
com.google.crypto.tink.proto.AesCtrKeyFormat: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.AesCtrKey aesCtrKey_
com.google.crypto.tink.proto.AesCtrParams: com.google.crypto.tink.proto.AesCtrParams DEFAULT_INSTANCE
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
com.google.crypto.tink.proto.AesSivKey: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.KeyTypeEntry: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int STATUS_FIELD_NUMBER
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int OUTPUT_PREFIX_TYPE_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset$Key: int keyId_
com.google.crypto.tink.proto.KeyTemplate: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesSivKeyFormat: int keySize_
com.google.crypto.tink.proto.KeyTypeEntry: int TYPE_URL_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.HmacParams: int TAG_SIZE_FIELD_NUMBER
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
com.google.crypto.tink.proto.AesGcmSivKey: int VERSION_FIELD_NUMBER
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
com.google.crypto.tink.proto.Keyset: int primaryKeyId_
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int KEY_VALUE_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxKeyFormat: com.google.crypto.tink.proto.AesEaxKeyFormat DEFAULT_INSTANCE
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrKey: int PARAMS_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
com.google.crypto.tink.proto.RegistryConfig: int ENTRY_FIELD_NUMBER
com.google.crypto.tink.proto.KmsAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
com.google.crypto.tink.proto.Keyset: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.InvokeOnCancelling: int _invoked
com.google.crypto.tink.proto.AesEaxParams: int ivSize_
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
kotlinx.coroutines.JobSupport: java.lang.Object _state
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
com.google.crypto.tink.proto.AesGcmSivKey: int version_
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
com.google.crypto.tink.proto.AesSivKeyFormat: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKey: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
com.google.crypto.tink.proto.AesCtrKey: int version_
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
com.google.crypto.tink.proto.KmsAeadKey: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.AesSivKeyFormat: com.google.crypto.tink.proto.AesSivKeyFormat DEFAULT_INSTANCE
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: int version_
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.proto.AesCtrHmacAeadKey DEFAULT_INSTANCE
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.google.crypto.tink.proto.XChaCha20Poly1305Key: int version_
com.google.crypto.tink.proto.KeyData: int TYPE_URL_FIELD_NUMBER
com.google.crypto.tink.proto.AesCmacKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.AesSivKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.RegistryConfig: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite: int memoizedSerializedSize
com.google.crypto.tink.proto.AesSivKeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
com.google.crypto.tink.proto.KeyData: int keyMaterialType_
com.google.crypto.tink.proto.KeysetInfo$KeyInfo: int TYPE_URL_FIELD_NUMBER
com.google.crypto.tink.proto.AesGcmKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.KmsAeadKeyFormat: int KEY_URI_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.proto.HmacParams params_
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
io.flutter.embedding.engine.FlutterJNI: float displayDensity
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.proto.HmacKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat: com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.XChaCha20Poly1305KeyFormat: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.HmacKeyFormat: int PARAMS_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKeyFormat: com.google.crypto.tink.proto.HmacKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCmacParams: int TAG_SIZE_FIELD_NUMBER
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
com.google.crypto.tink.proto.AesEaxKeyFormat: int keySize_
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
com.google.crypto.tink.proto.AesGcmKeyFormat: int version_
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.proto.AesEaxKey DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesEaxKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.ChaCha20Poly1305Key: int version_
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
com.google.crypto.tink.proto.AesEaxParams: int IV_SIZE_FIELD_NUMBER
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.EncryptedKeyset: com.google.crypto.tink.shaded.protobuf.Parser PARSER
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
com.google.crypto.tink.proto.EncryptedKeyset: int KEYSET_INFO_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKey: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesSivKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
com.google.crypto.tink.proto.AesEaxParams: com.google.crypto.tink.proto.AesEaxParams DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacKey: int VERSION_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKey: int AES_CTR_KEY_FIELD_NUMBER
com.google.crypto.tink.proto.HmacKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.AesCtrKey: com.google.crypto.tink.shaded.protobuf.ByteString keyValue_
com.google.crypto.tink.proto.AesEaxKeyFormat: int KEY_SIZE_FIELD_NUMBER
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.AesCtrKeyFormat aesCtrKeyFormat_
com.google.crypto.tink.proto.AesCmacKeyFormat: int keySize_
com.google.crypto.tink.proto.KeyTypeEntry: int KEY_MANAGER_VERSION_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
io.flutter.embedding.engine.FlutterJNI: float displayWidth
com.google.crypto.tink.proto.ChaCha20Poly1305Key: com.google.crypto.tink.shaded.protobuf.Parser PARSER
com.google.crypto.tink.proto.AesCmacParams: com.google.crypto.tink.proto.AesCmacParams DEFAULT_INSTANCE
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: int AES_CTR_KEY_FORMAT_FIELD_NUMBER
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
com.google.crypto.tink.proto.AesGcmSivKeyFormat: int KEY_SIZE_FIELD_NUMBER
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
io.flutter.embedding.engine.FlutterJNI: float displayHeight
com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat: com.google.crypto.tink.proto.AesCtrHmacAeadKeyFormat DEFAULT_INSTANCE
com.google.crypto.tink.proto.HmacKeyFormat: int keySize_
com.google.crypto.tink.proto.AesCtrParams: int ivSize_
com.google.crypto.tink.proto.KmsEnvelopeAeadKey: com.google.crypto.tink.proto.KmsEnvelopeAeadKeyFormat params_
kotlinx.coroutines.DefaultExecutor: int debugStatus
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType[] values()
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility: com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility[] values()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm valueOf(java.lang.String)
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
com.google.crypto.tink.shaded.protobuf.FieldType$Collection: com.google.crypto.tink.shaded.protobuf.FieldType$Collection valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder: com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder valueOf(java.lang.String)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme valueOf(java.lang.String)
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
androidx.appcompat.widget.SearchView: void setIconified(boolean)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
androidx.appcompat.widget.SearchView: void setInputType(int)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List)
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
androidx.lifecycle.ReportFragment: ReportFragment()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility: com.google.crypto.tink.config.internal.TinkFipsUtil$AlgorithmFipsCompatibility valueOf(java.lang.String)
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType[] values()
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
com.google.crypto.tink.shaded.protobuf.ProtoSyntax: com.google.crypto.tink.shaded.protobuf.ProtoSyntax[] values()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme[] values()
androidx.security.crypto.MasterKey$Builder$Api23Impl: java.lang.String getKeystoreAlias(android.security.keystore.KeyGenParameterSpec)
androidx.security.crypto.MasterKey$KeyScheme: androidx.security.crypto.MasterKey$KeyScheme[] values()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm valueOf(java.lang.String)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.appcompat.widget.SearchView: int getMaxWidth()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.exifinterface.media.ExifInterfaceUtils$Api23Impl: void setDataSource(android.media.MediaMetadataRetriever,android.media.MediaDataSource)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
com.google.crypto.tink.proto.KeyData$KeyMaterialType: com.google.crypto.tink.proto.KeyData$KeyMaterialType valueOf(java.lang.String)
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
androidx.core.app.NotificationManagerCompat$Api24Impl: int getImportance(android.app.NotificationManager)
com.google.crypto.tink.proto.HashType: com.google.crypto.tink.proto.HashType[] values()
com.google.crypto.tink.shaded.protobuf.ProtoSyntax: com.google.crypto.tink.shaded.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
com.it_nomads.fluttersecurestorage.FlutterSecureStoragePlugin: FlutterSecureStoragePlugin()
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$700(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType: com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.google.crypto.tink.shaded.protobuf.JavaType: com.google.crypto.tink.shaded.protobuf.JavaType[] values()
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.security.crypto.EncryptedSharedPreferences$EncryptedType: androidx.security.crypto.EncryptedSharedPreferences$EncryptedType[] values()
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
androidx.core.app.ActivityCompat$Api32Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
androidx.appcompat.widget.SearchView: void setImeOptions(int)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.startup.InitializationProvider: InitializationProvider()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke: com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
com.google.crypto.tink.shaded.protobuf.JavaType: com.google.crypto.tink.shaded.protobuf.JavaType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
com.google.crypto.tink.KeyTemplate$OutputPrefixType: com.google.crypto.tink.KeyTemplate$OutputPrefixType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.app.ActivityCompat$Api23Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
com.google.crypto.tink.proto.OutputPrefixType: com.google.crypto.tink.proto.OutputPrefixType[] values()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
com.example.handwrite_to_md.MainActivity: MainActivity()
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin: FlutterAndroidLifecyclePlugin()
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: java.io.FileDescriptor dup(java.io.FileDescriptor)
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefValueEncryptionScheme[] values()
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
com.google.crypto.tink.proto.KeyData$KeyMaterialType: com.google.crypto.tink.proto.KeyData$KeyMaterialType[] values()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: void close(java.io.FileDescriptor)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.security.crypto.MasterKey$Builder$Api23Impl$Api28Impl: void setIsStrongBoxBacked(android.security.keystore.KeyGenParameterSpec$Builder)
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
com.mr.flutter.plugin.filepicker.FilePickerPlugin: FilePickerPlugin()
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
com.google.crypto.tink.proto.KeyStatusType: com.google.crypto.tink.proto.KeyStatusType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
androidx.appcompat.widget.SearchView: int getInputType()
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.KeyCipherAlgorithm[] values()
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
com.google.crypto.tink.shaded.protobuf.FieldType: com.google.crypto.tink.shaded.protobuf.FieldType valueOf(java.lang.String)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.widget.NestedScrollView: int getScrollRange()
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
com.google.crypto.tink.proto.HashType: com.google.crypto.tink.proto.HashType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.core.app.ActivityCompat$Api23Impl: void onSharedElementsReady(java.lang.Object)
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.core.app.ActivityCompat$Api23Impl: void requestPermissions(android.app.Activity,java.lang.String[],int)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke: com.google.crypto.tink.shaded.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType: com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera valueOf(java.lang.String)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
androidx.core.app.ActivityCompat$Api31Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType valueOf(java.lang.String)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List)
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
com.google.crypto.tink.proto.KeyStatusType: com.google.crypto.tink.proto.KeyStatusType valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.core.app.NotificationManagerCompat$Api24Impl: boolean areNotificationsEnabled(android.app.NotificationManager)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.security.crypto.EncryptedSharedPreferences$EncryptedType: androidx.security.crypto.EncryptedSharedPreferences$EncryptedType valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: long lseek(java.io.FileDescriptor,long,int)
com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType: com.google.crypto.tink.shaded.protobuf.WireFormat$FieldType[] values()
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
io.flutter.plugins.imagepicker.ImagePickerPlugin: ImagePickerPlugin()
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
androidx.security.crypto.MasterKey$Builder$Api23Impl$Api30Impl: void setUserAuthenticationParameters(android.security.keystore.KeyGenParameterSpec$Builder,int,int)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
androidx.core.app.ActivityCompat$Api31Impl: boolean isLaunchedFromBubble(android.app.Activity)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
io.flutter.plugins.imagepicker.ImagePickerFileProvider: ImagePickerFileProvider()
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder: com.google.crypto.tink.shaded.protobuf.Writer$FieldOrder[] values()
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType: com.google.crypto.tink.shaded.protobuf.WireFormat$JavaType[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
com.google.crypto.tink.shaded.protobuf.FieldType: com.google.crypto.tink.shaded.protobuf.FieldType[] values()
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice[] values()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme: androidx.security.crypto.EncryptedSharedPreferences$PrefKeyEncryptionScheme valueOf(java.lang.String)
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
kotlin.random.Random: Random()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
com.google.crypto.tink.KeyTemplate$OutputPrefixType: com.google.crypto.tink.KeyTemplate$OutputPrefixType[] values()
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void lambda$dequeueImage$0()
com.google.crypto.tink.shaded.protobuf.FieldType$Collection: com.google.crypto.tink.shaded.protobuf.FieldType$Collection[] values()
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm: com.it_nomads.fluttersecurestorage.ciphers.StorageCipherAlgorithm[] values()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
androidx.security.crypto.MasterKey$KeyScheme: androidx.security.crypto.MasterKey$KeyScheme valueOf(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera[] values()
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference)
kotlin.collections.AbstractList: AbstractList()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
com.baseflow.permissionhandler.PermissionHandlerPlugin: PermissionHandlerPlugin()
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
androidx.recyclerview.widget.RecyclerView: int getBaseline()
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer)
androidx.core.content.FileProvider$Api21Impl: java.io.File[] getExternalMediaDirs(android.content.Context)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
com.google.crypto.tink.proto.OutputPrefixType: com.google.crypto.tink.proto.OutputPrefixType valueOf(java.lang.String)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.appcompat.widget.Toolbar: void setTitle(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
