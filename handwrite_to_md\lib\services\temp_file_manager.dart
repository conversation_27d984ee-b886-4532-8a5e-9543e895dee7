import 'dart:io' as io;
import 'package:path/path.dart' as path;

class TempFileManager {
  static const String _tempDirName = 'webdav_temp';
  static const int _maxCacheAgeHours = 24; // 24小时后自动清理
  static const int _maxCacheSizeMB = 100; // 最大缓存100MB

  /// 获取临时文件目录
  static Future<io.Directory> getTempDirectory() async {
    final tempDir = io.Directory.systemTemp;
    final webdavTempDir = io.Directory(path.join(tempDir.path, _tempDirName));
    
    if (!await webdavTempDir.exists()) {
      await webdavTempDir.create(recursive: true);
    }
    
    return webdavTempDir;
  }

  /// 生成临时文件路径
  static Future<String> generateTempFilePath(String fileName) async {
    final tempDir = await getTempDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final nameWithoutExt = path.basenameWithoutExtension(fileName);
    final ext = path.extension(fileName);
    final uniqueFileName = '${nameWithoutExt}_$timestamp$ext';
    return path.join(tempDir.path, uniqueFileName);
  }

  /// 创建会话临时目录（用于单次批量处理）
  static Future<io.Directory> createSessionDirectory() async {
    final tempDir = await getTempDirectory();
    final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
    final sessionDir = io.Directory(path.join(tempDir.path, 'session_$sessionId'));
    
    await sessionDir.create(recursive: true);
    return sessionDir;
  }

  /// 清理指定会话目录
  static Future<bool> cleanupSessionDirectory(io.Directory sessionDir) async {
    try {
      if (await sessionDir.exists()) {
        await sessionDir.delete(recursive: true);
        return true;
      }
      return false;
    } catch (e) {
      print('清理会话目录失败: $e');
      return false;
    }
  }

  /// 清理所有过期的临时文件
  static Future<void> cleanupExpiredFiles() async {
    try {
      final tempDir = await getTempDirectory();
      if (!await tempDir.exists()) return;

      // 安全检查：确保只清理临时目录内的文件
      final tempDirPath = tempDir.path;
      if (!tempDirPath.contains(_tempDirName)) {
        print('安全检查失败：临时目录路径不正确: $tempDirPath');
        return;
      }

      final cutoffTime = DateTime.now().subtract(const Duration(hours: _maxCacheAgeHours));

      await for (final entity in tempDir.list()) {
        try {
          // 双重安全检查：确保文件在临时目录内
          if (!entity.path.startsWith(tempDirPath)) {
            print('跳过非临时目录文件: ${entity.path}');
            continue;
          }

          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffTime)) {
            await entity.delete(recursive: true);
            print('已删除过期临时文件: ${entity.path}');
          }
        } catch (e) {
          print('删除过期文件失败: ${entity.path}, 错误: $e');
        }
      }
    } catch (e) {
      print('清理过期文件失败: $e');
    }
  }

  /// 检查并清理缓存大小
  static Future<void> cleanupBySize() async {
    try {
      final tempDir = await getTempDirectory();
      if (!await tempDir.exists()) return;

      // 获取所有文件及其大小
      final files = <io.FileSystemEntity, int>{};
      int totalSize = 0;

      await for (final entity in tempDir.list(recursive: true)) {
        if (entity is io.File) {
          try {
            final size = await entity.length();
            files[entity] = size;
            totalSize += size;
          } catch (e) {
            print('获取文件大小失败: ${entity.path}');
          }
        }
      }

      // 如果超过最大缓存大小，删除最旧的文件
      if (totalSize > _maxCacheSizeMB * 1024 * 1024) {
        // 按修改时间排序
        final sortedFiles = files.entries.toList();
        sortedFiles.sort((a, b) {
          try {
            final aStat = a.key.statSync();
            final bStat = b.key.statSync();
            return aStat.modified.compareTo(bStat.modified);
          } catch (e) {
            return 0;
          }
        });

        // 删除最旧的文件直到大小合适
        int currentSize = totalSize;
        for (final entry in sortedFiles) {
          if (currentSize <= _maxCacheSizeMB * 1024 * 1024) break;
          
          try {
            await entry.key.delete();
            currentSize -= entry.value;
            print('已删除缓存文件: ${entry.key.path}');
          } catch (e) {
            print('删除缓存文件失败: ${entry.key.path}');
          }
        }
      }
    } catch (e) {
      print('按大小清理缓存失败: $e');
    }
  }

  /// 获取缓存统计信息
  static Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final tempDir = await getTempDirectory();
      if (!await tempDir.exists()) {
        return {
          'fileCount': 0,
          'totalSizeMB': 0.0,
          'oldestFile': null,
          'newestFile': null,
        };
      }

      int fileCount = 0;
      int totalSize = 0;
      DateTime? oldestTime;
      DateTime? newestTime;

      await for (final entity in tempDir.list(recursive: true)) {
        if (entity is io.File) {
          try {
            fileCount++;
            totalSize += await entity.length();
            
            final stat = await entity.stat();
            if (oldestTime == null || stat.modified.isBefore(oldestTime)) {
              oldestTime = stat.modified;
            }
            if (newestTime == null || stat.modified.isAfter(newestTime)) {
              newestTime = stat.modified;
            }
          } catch (e) {
            print('获取文件统计失败: ${entity.path}');
          }
        }
      }

      return {
        'fileCount': fileCount,
        'totalSizeMB': totalSize / (1024 * 1024),
        'oldestFile': oldestTime,
        'newestFile': newestTime,
      };
    } catch (e) {
      print('获取缓存统计失败: $e');
      return {
        'fileCount': 0,
        'totalSizeMB': 0.0,
        'oldestFile': null,
        'newestFile': null,
      };
    }
  }

  /// 清理所有临时文件
  static Future<bool> clearAllCache() async {
    try {
      final tempDir = await getTempDirectory();
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
        // 重新创建目录
        await tempDir.create(recursive: true);
        return true;
      }
      return false;
    } catch (e) {
      print('清理所有缓存失败: $e');
      return false;
    }
  }

  /// 执行完整的缓存维护
  static Future<void> performMaintenance() async {
    await cleanupExpiredFiles();
    await cleanupBySize();
  }

  /// 检查文件是否存在于缓存中
  static Future<io.File?> getCachedFile(String fileName) async {
    try {
      final tempDir = await getTempDirectory();
      final file = io.File(path.join(tempDir.path, fileName));
      
      if (await file.exists()) {
        // 检查文件是否过期
        final stat = await file.stat();
        final cutoffTime = DateTime.now().subtract(const Duration(hours: _maxCacheAgeHours));
        
        if (stat.modified.isAfter(cutoffTime)) {
          return file;
        } else {
          // 文件过期，删除它
          await file.delete();
        }
      }
      
      return null;
    } catch (e) {
      print('检查缓存文件失败: $e');
      return null;
    }
  }
}
